import React, { createContext, useReducer, useContext, Dispatch } from 'react';
import { AppAction, appReducer, initialState } from './AppReducer';
import { AppState } from '@/types/app-context';

type AppContextType = {
  state: AppState;
  dispatch: Dispatch<AppAction>;
};

const AppContext = createContext<AppContextType>({
  state: initialState,
  dispatch: () => null, // Placeholder function
});

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook để truy cập Context
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider');
  }
  return context;
};